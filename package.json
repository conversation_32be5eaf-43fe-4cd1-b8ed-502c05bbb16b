{"name": "code-", "version": "1.0.0", "description": "", "main": "index.js", "directories": {"doc": "docs", "test": "test"}, "dependencies": {"pinia": "^3.0.3", "vue": "3.5.18"}, "devDependencies": {"nodemon": "^3.1.10"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/hviwen/code-.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/hviwen/code-/issues"}, "homepage": "https://github.com/hviwen/code-#readme"}